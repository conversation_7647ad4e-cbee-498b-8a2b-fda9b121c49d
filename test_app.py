"""
Test script for the Excel Filtering Tool
This script creates various test Excel files to validate the application functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def create_test_files():
    """Create various test Excel files"""
    
    # Test 1: Basic employee data
    print("Creating basic employee test file...")
    employee_data = {
        'ID': range(1, 51),
        'Name': [f'Employee_{i}' for i in range(1, 51)],
        'Department': [random.choice(['Sales', 'Marketing', 'IT', 'HR', 'Finance']) for _ in range(50)],
        'Salary': [random.randint(30000, 120000) for _ in range(50)],
        'Age': [random.randint(22, 65) for _ in range(50)],
        'City': [random.choice(['New York', 'Los Angeles', 'Chicago', 'Houston']) for _ in range(50)]
    }
    pd.DataFrame(employee_data).to_excel('test_employees.xlsx', index=False)
    
    # Test 2: Sales data with dates
    print("Creating sales test file...")
    dates = [datetime.now() - timedelta(days=x) for x in range(100)]
    sales_data = {
        'Date': dates,
        'Product': [random.choice(['Product A', 'Product B', 'Product C', 'Product D']) for _ in range(100)],
        'Sales_Amount': [round(random.uniform(100, 5000), 2) for _ in range(100)],
        'Quantity': [random.randint(1, 50) for _ in range(100)],
        'Region': [random.choice(['North', 'South', 'East', 'West']) for _ in range(100)],
        'Salesperson': [f'Sales_{i}' for i in random.choices(range(1, 11), k=100)]
    }
    pd.DataFrame(sales_data).to_excel('test_sales.xlsx', index=False)
    
    # Test 3: Data with missing values
    print("Creating test file with missing values...")
    incomplete_data = {
        'ID': range(1, 21),
        'Name': [f'Person_{i}' if i % 3 != 0 else None for i in range(1, 21)],
        'Score': [random.randint(0, 100) if i % 4 != 0 else None for i in range(20)],
        'Category': [random.choice(['A', 'B', 'C', None]) for _ in range(20)],
        'Value': [round(random.uniform(0, 1000), 2) if i % 5 != 0 else None for i in range(20)]
    }
    pd.DataFrame(incomplete_data).to_excel('test_missing_data.xlsx', index=False)
    
    # Test 4: Large dataset
    print("Creating large test file...")
    large_data = {
        'ID': range(1, 1001),
        'Category': [f'Cat_{random.randint(1, 20)}' for _ in range(1000)],
        'Value1': [random.randint(1, 1000) for _ in range(1000)],
        'Value2': [round(random.uniform(0, 100), 2) for _ in range(1000)],
        'Status': [random.choice(['Active', 'Inactive', 'Pending']) for _ in range(1000)]
    }
    pd.DataFrame(large_data).to_excel('test_large_data.xlsx', index=False)
    
    print("All test files created successfully!")
    print("\nTest files created:")
    print("- test_employees.xlsx (basic employee data)")
    print("- test_sales.xlsx (sales data with dates)")
    print("- test_missing_data.xlsx (data with missing values)")
    print("- test_large_data.xlsx (large dataset with 1000 rows)")

def run_basic_tests():
    """Run basic functionality tests"""
    print("\n" + "="*50)
    print("TESTING INSTRUCTIONS")
    print("="*50)
    print("\n1. Run the main application: python main.py")
    print("\n2. Test file loading:")
    print("   - Load each test file using 'Browse Excel File' button")
    print("   - Verify file information is displayed correctly")
    print("   - Check that all columns are shown in the filter section")
    
    print("\n3. Test filtering:")
    print("   - Try text filters (partial matches, case insensitive)")
    print("   - Try numeric range filters (min/max values)")
    print("   - Test combination of multiple filters")
    print("   - Use F5 to apply filters, Esc to clear")
    
    print("\n4. Test results display:")
    print("   - Verify filtered results are shown correctly")
    print("   - Check row count display")
    print("   - Test scrolling for large datasets")
    
    print("\n5. Test export functionality:")
    print("   - Export filtered data using 'Export Filtered Data' button")
    print("   - Verify exported file contains only filtered rows")
    print("   - Test Ctrl+S keyboard shortcut")
    
    print("\n6. Test error handling:")
    print("   - Try loading non-Excel files")
    print("   - Test with empty files")
    print("   - Test invalid filter values")
    
    print("\n7. Test keyboard shortcuts:")
    print("   - Ctrl+O: Open file")
    print("   - Ctrl+S: Export data")
    print("   - F5: Apply filters")
    print("   - Esc: Clear filters")

if __name__ == "__main__":
    create_test_files()
    run_basic_tests()
