#!/usr/bin/env python3
"""
Demo data generator for Excel Filtering Tool
Creates sample Excel files to demonstrate the application features
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def create_sample_data():
    """Create sample data for demonstration"""
    
    # Set random seed for reproducible data
    np.random.seed(42)
    random.seed(42)
    
    # Sample data
    companies = ['BIOCHE', 'KUWAIT', 'JAMJOC', 'GLENNA', 'ACETAL', 'ACLOC', 'ACRETH']
    item_types = ['ACC', 'ACETAE', 'ACLOC', 'ACRETH']
    currencies = ['USD', 'EUR', 'GBP', 'JPY']
    
    # Generate 500 rows of sample data
    n_rows = 500
    
    data = {
        'ITEM_NO': [f"{random.choice(item_types)}{random.randint(1, 99)}" for _ in range(n_rows)],
        'ITEM_NAME': [f"Item {i}" for i in range(1, n_rows + 1)],
        'CUST_NO': [random.randint(1000, 9999) for _ in range(n_rows)],
        'CUST_NAME': [f"Customer {random.randint(1, 100)}" for _ in range(n_rows)],
        'INVOICE_NO': [random.randint(100000, 999999) for _ in range(n_rows)],
        'INVOICE_DATE': [(datetime.now() - timedelta(days=random.randint(0, 365))).strftime('%Y-%m-%d') for _ in range(n_rows)],
        'CORP_NO': [random.choice(companies) for _ in range(n_rows)],
        'CORP_NAME': [f"{random.choice(companies)} Corp" for _ in range(n_rows)],
        'EXPIRY_DATE': [(datetime.now() + timedelta(days=random.randint(30, 730))).strftime('%Y-%m-%d') for _ in range(n_rows)],
        'FACTORY_ORDER_NO': [f"FO{random.randint(10000, 99999)}" for _ in range(n_rows)],
        'DISC_PERCENT': [round(random.uniform(0, 25), 2) for _ in range(n_rows)],
        'PUR_PRICE': [round(random.uniform(10, 1000), 2) for _ in range(n_rows)],
        'PUR_QTY': [random.randint(1, 100) for _ in range(n_rows)],
        'PUR_BONUS': [random.randint(0, 10) for _ in range(n_rows)],
        'NET_PUR_AMOUNT': [round(random.uniform(100, 5000), 2) for _ in range(n_rows)],
        'TOTAL_PUR_AMOUNT': [round(random.uniform(100, 5000), 2) for _ in range(n_rows)],
        'TOTAL_DISC1': [round(random.uniform(0, 500), 2) for _ in range(n_rows)],
        'TOTAL_DISC2': [round(random.uniform(0, 200), 2) for _ in range(n_rows)],
        'SC2': [round(random.uniform(-50, 50), 2) for _ in range(n_rows)],
        'SC3': [round(random.uniform(-20, 20), 2) for _ in range(n_rows)]
    }
    
    return pd.DataFrame(data)

def main():
    """Generate and save sample data"""
    print("Generating sample data for Excel Filtering Tool demo...")
    
    # Create sample data
    df = create_sample_data()
    
    # Save to Excel file
    filename = "sample_data.xlsx"
    df.to_excel(filename, index=False)
    
    print(f"✅ Sample data saved to {filename}")
    print(f"📊 Generated {len(df)} rows with {len(df.columns)} columns")
    print("\nColumns included:")
    for col in df.columns:
        print(f"  • {col}")
    
    print(f"\n🚀 You can now test the Excel Filtering Tool with this sample data!")
    print(f"   Run: python main.py")
    print(f"   Then open: {filename}")

if __name__ == "__main__":
    main()
