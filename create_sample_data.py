import pandas as pd
import random
from datetime import datetime, timedelta

# Create sample data for testing
def create_sample_excel():
    # Generate sample data
    data = {
        'Employee_ID': range(1, 101),
        'Name': [f'Employee_{i}' for i in range(1, 101)],
        'Department': [random.choice(['Sales', 'Marketing', 'IT', 'HR', 'Finance']) for _ in range(100)],
        'Salary': [random.randint(30000, 120000) for _ in range(100)],
        'Age': [random.randint(22, 65) for _ in range(100)],
        'Experience_Years': [random.randint(0, 40) for _ in range(100)],
        'City': [random.choice(['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix']) for _ in range(100)],
        'Performance_Rating': [random.choice(['Excellent', 'Good', 'Average', 'Below Average']) for _ in range(100)]
    }
    
    df = pd.DataFrame(data)
    df.to_excel('sample_employees.xlsx', index=False)
    print("Sample Excel file 'sample_employees.xlsx' created successfully!")

if __name__ == "__main__":
    create_sample_excel()
