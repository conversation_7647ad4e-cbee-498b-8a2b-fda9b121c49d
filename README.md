# Excel Filtering Tool

A desktop application for filtering Excel files with an intuitive GUI.

## Features
- Load Excel files (.xlsx, .xls)
- Apply various filters to data
- View filtered results
- Export filtered data to new Excel file

## Installation

1. Install Python 3.8 or higher
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

Run the application:
```
python main.py
```

## Requirements
- Python 3.8+
- pandas
- openpyxl
- xlrd
- tkinter (usually included with Python)
