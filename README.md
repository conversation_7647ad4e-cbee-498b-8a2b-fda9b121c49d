# 📊 Excel Filtering Tool - Modern Edition

A modern, user-friendly desktop application for filtering and analyzing Excel files with an intuitive graphical interface.

## ✨ New Features & UI/UX Improvements

### 🎨 **Modern Visual Design**
- **Clean, professional interface** with modern typography and spacing
- **Emoji icons** throughout the interface for better visual hierarchy
- **Improved color scheme** with accessible, professional colors
- **Better layout** with proper spacing and visual grouping
- **Alternating row colors** in the results table for better readability

### 🚀 **Enhanced Functionality**
- **Drag & Drop Support** - Simply drag Excel files into the application
- **Quick Search** - Search across all columns with real-time filtering (Ctrl+F)
- **Recent Files Menu** - Quick access to recently opened files
- **Filter Presets** - Save and load common filter combinations
- **Auto-sizing columns** - Columns automatically resize based on content
- **Better file status indicators** - Visual feedback for file loading states

### ⌨️ **Improved User Experience**
- **Enhanced keyboard shortcuts**:
  - `Ctrl+O` - Open file
  - `Ctrl+S` - Export data
  - `Ctrl+F` - Focus search
  - `F5` - Apply filters
  - `Esc` - Clear filters
- **Placeholder text** in filter inputs
- **Loading indicators** for file operations
- **Better error messages** with clear icons

### 📋 **Filter Improvements**
- **Type indicators** for columns (📝 text, 🔢 numeric, 📄 other)
- **Improved filter layout** with better spacing
- **Dropdown suggestions** for text filters (when applicable)
- **Range information** for numeric filters
- **Active filter counter** in results display
- **Filter preset system** for saving/loading common filters

## 🛠️ Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
   Or run the provided batch file:
   ```bash
   install_dependencies.bat
   ```

2. **Run the application:**
   ```bash
   python main.py
   ```

## 📦 Dependencies

- `pandas>=2.0.0` - Data manipulation and analysis
- `openpyxl>=3.1.0` - Excel file reading/writing
- `xlrd>=2.0.0` - Legacy Excel file support
- `tkinterdnd2>=0.3.0` - Drag and drop functionality

## 🎯 Usage

### Loading Files
- **Browse**: Click "📂 Open File" or use Ctrl+O
- **Drag & Drop**: Simply drag Excel files into the application
- **Recent Files**: Access recently opened files from the File menu

### Filtering Data
- **Quick Search**: Use the search box at the top of the filters panel (Ctrl+F)
- **Column Filters**: Set specific filters for each column
- **Text Filters**: Type to filter text columns, use dropdown for common values
- **Numeric Filters**: Set min/max ranges for numeric columns

### Filter Presets
- **Save**: Filter → Save Filter Preset (after setting up filters)
- **Load**: Filter → Load Filter Preset (to apply saved filters)

### Exporting Results
- Click "💾 Export" or use Ctrl+S to save filtered data to a new Excel file
