import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import pandas as pd
import os
import json
from typing import Optional, Dict, Any
from tkinterdnd2 import DND_FILES, TkinterDnD


class ExcelFilterTool:
    def __init__(self, root):
        self.root = root
        self.root.title("📊 Excel Filtering Tool - Modern Edition")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 600)

        # Data storage
        self.df: Optional[pd.DataFrame] = None
        self.filtered_df: Optional[pd.DataFrame] = None
        self.file_path: Optional[str] = None
        self.recent_files = self.load_recent_files()
        self.filter_presets = self.load_filter_presets()

        # Configure modern styling
        self.setup_styling()

        # Enable drag and drop
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_file_drop)

        # Bind keyboard shortcuts
        self.root.bind('<Control-o>', lambda e: self.browse_file())
        self.root.bind('<Control-s>', lambda e: self.export_data())
        self.root.bind('<F5>', lambda e: self.apply_filters())
        self.root.bind('<Escape>', lambda e: self.clear_filters())
        self.root.bind('<Control-f>', lambda e: self.focus_search())

        # Create main interface
        self.create_menu()
        self.create_widgets()

    def setup_styling(self):
        """Configure modern styling for the application"""
        style = ttk.Style()

        # Use a modern theme
        style.theme_use('clam')

        # Define color palette
        colors = {
            'primary': '#2563eb',      # Blue
            'primary_light': '#3b82f6',
            'secondary': '#64748b',    # Slate
            'success': '#10b981',      # Green
            'warning': '#f59e0b',      # Amber
            'danger': '#ef4444',       # Red
            'background': '#f8fafc',   # Light gray
            'surface': '#ffffff',      # White
            'text': '#1e293b',         # Dark gray
            'text_light': '#64748b'    # Light gray
        }

        # Configure styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 16, 'bold'),
                       foreground=colors['text'])

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=colors['text'])

        style.configure('Info.TLabel',
                       font=('Segoe UI', 9),
                       foreground=colors['text_light'])

        style.configure('Primary.TButton',
                       font=('Segoe UI', 10),
                       padding=(20, 8))

        style.configure('Secondary.TButton',
                       font=('Segoe UI', 9),
                       padding=(15, 6))

        style.configure('Success.TButton',
                       font=('Segoe UI', 10),
                       padding=(20, 8))

        # Configure frame styles
        style.configure('Card.TFrame',
                       relief='solid',
                       borderwidth=1)

        style.configure('Sidebar.TFrame',
                       background=colors['background'])

    def load_recent_files(self):
        """Load recent files from config"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    config = json.load(f)
                    return config.get('recent_files', [])
        except:
            pass
        return []

    def save_recent_files(self):
        """Save recent files to config"""
        try:
            config = {}
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    config = json.load(f)

            config['recent_files'] = self.recent_files

            with open('config.json', 'w') as f:
                json.dump(config, f, indent=2)
        except:
            pass

    def add_recent_file(self, file_path):
        """Add file to recent files list"""
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
        self.recent_files.insert(0, file_path)
        self.recent_files = self.recent_files[:10]  # Keep only 10 recent files
        self.save_recent_files()

    def load_filter_presets(self):
        """Load filter presets from config"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    config = json.load(f)
                    return config.get('filter_presets', {})
        except:
            pass
        return {}

    def save_filter_presets(self):
        """Save filter presets to config"""
        try:
            config = {}
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    config = json.load(f)

            config['filter_presets'] = self.filter_presets

            with open('config.json', 'w') as f:
                json.dump(config, f, indent=2)
        except:
            pass

    def on_file_drop(self, event):
        """Handle file drop events"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith(('.xlsx', '.xls')):
                self.load_excel_file(file_path)
            else:
                messagebox.showerror("Invalid File", "Please drop an Excel file (.xlsx or .xls)")

    def focus_search(self):
        """Focus on search entry if it exists"""
        if hasattr(self, 'search_var'):
            self.search_entry.focus_set()

    def create_menu(self):
        """Create menu bar with keyboard shortcuts"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="📂 Open Excel File...", command=self.browse_file, accelerator="Ctrl+O")

        # Recent files submenu
        if self.recent_files:
            recent_menu = tk.Menu(file_menu, tearoff=0)
            file_menu.add_cascade(label="📋 Recent Files", menu=recent_menu)
            for i, file_path in enumerate(self.recent_files[:10]):
                filename = os.path.basename(file_path)
                recent_menu.add_command(
                    label=f"{i+1}. {filename}",
                    command=lambda fp=file_path: self.load_excel_file(fp)
                )

        file_menu.add_separator()
        file_menu.add_command(label="💾 Export Filtered Data...", command=self.export_data, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="❌ Exit", command=self.root.quit)

        # Filter menu
        filter_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Filter", menu=filter_menu)
        filter_menu.add_command(label="🔍 Quick Search", command=self.focus_search, accelerator="Ctrl+F")
        filter_menu.add_separator()
        filter_menu.add_command(label="✅ Apply Filters", command=self.apply_filters, accelerator="F5")
        filter_menu.add_command(label="🗑️ Clear All Filters", command=self.clear_filters, accelerator="Esc")
        filter_menu.add_separator()
        filter_menu.add_command(label="💾 Save Filter Preset...", command=self.save_filter_preset)
        filter_menu.add_command(label="📂 Load Filter Preset...", command=self.load_filter_preset)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="ℹ️ About", command=self.show_about)

    def create_widgets(self):
        # Main container with modern styling
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)  # Give more space to results
        main_frame.rowconfigure(3, weight=1)

        # Header section with title and quick actions
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(1, weight=1)

        title_label = ttk.Label(header_frame, text="Excel Data Filter & Analyzer", style="Title.TLabel")
        title_label.grid(row=0, column=0, sticky=tk.W)

        # Quick action buttons
        quick_actions = ttk.Frame(header_frame)
        quick_actions.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(quick_actions, text="📂 Open File", style="Primary.TButton",
                  command=self.browse_file).pack(side=tk.RIGHT, padx=(10, 0))

        # File selection section with drag & drop
        file_frame = ttk.LabelFrame(main_frame, text="📁 File Selection", padding="15", style="Card.TFrame")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)

        # Drag & drop area
        drop_frame = ttk.Frame(file_frame, style="Card.TFrame", padding="20")
        drop_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        drop_frame.columnconfigure(0, weight=1)

        drop_label = ttk.Label(drop_frame, text="📎 Drag & Drop Excel files here or click Browse",
                              style="Info.TLabel", anchor="center")
        drop_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        ttk.Button(file_frame, text="🔍 Browse Files", style="Secondary.TButton",
                  command=self.browse_file).grid(row=1, column=0, padx=(0, 10), sticky=tk.W)

        self.file_label = ttk.Label(file_frame, text="No file selected", style="Info.TLabel")
        self.file_label.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        # File status indicator
        self.status_label = ttk.Label(file_frame, text="", style="Info.TLabel")
        self.status_label.grid(row=1, column=2, sticky=tk.E)

        # File info section with better layout
        self.info_frame = ttk.LabelFrame(main_frame, text="📊 File Information", padding="15", style="Card.TFrame")
        self.info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        self.info_frame.columnconfigure(0, weight=1)

        # Info display with better formatting
        info_display_frame = ttk.Frame(self.info_frame)
        info_display_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        info_display_frame.columnconfigure(1, weight=1)

        self.rows_label = ttk.Label(info_display_frame, text="Rows: -", style="Info.TLabel")
        self.rows_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        self.cols_label = ttk.Label(info_display_frame, text="Columns: -", style="Info.TLabel")
        self.cols_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        self.size_label = ttk.Label(info_display_frame, text="Size: -", style="Info.TLabel")
        self.size_label.grid(row=0, column=2, sticky=tk.W)

        # Main content area
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(1, weight=2)
        content_frame.rowconfigure(0, weight=1)

        # Filter section (left sidebar)
        self.filter_frame = ttk.LabelFrame(content_frame, text="🔍 Filters & Search",
                                         padding="15", style="Sidebar.TFrame")
        self.filter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Quick search at top of filters
        search_frame = ttk.Frame(self.filter_frame)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        search_frame.columnconfigure(1, weight=1)

        ttk.Label(search_frame, text="🔍 Quick Search:", style="Heading.TLabel").grid(row=0, column=0, sticky=tk.W)

        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        # Filter controls container
        self.filter_controls_frame = ttk.Frame(self.filter_frame)
        self.filter_controls_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.filter_frame.rowconfigure(1, weight=1)

        # Results section (main area)
        results_frame = ttk.LabelFrame(content_frame, text="📋 Results", padding="15")
        results_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(1, weight=1)

        # Results toolbar
        results_toolbar = ttk.Frame(results_frame)
        results_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        results_toolbar.columnconfigure(1, weight=1)

        self.result_label = ttk.Label(results_toolbar, text="No data loaded", style="Info.TLabel")
        self.result_label.grid(row=0, column=0, sticky=tk.W)

        # Export and action buttons
        action_buttons = ttk.Frame(results_toolbar)
        action_buttons.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(action_buttons, text="💾 Export", style="Success.TButton",
                  command=self.export_data).pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(action_buttons, text="🔄 Refresh", style="Secondary.TButton",
                  command=self.apply_filters).pack(side=tk.RIGHT, padx=(5, 0))

        # Results table with modern styling
        table_frame = ttk.Frame(results_frame)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # Create treeview with better styling
        self.tree = ttk.Treeview(table_frame, show='headings')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure treeview styling
        style = ttk.Style()
        style.configure("Treeview.Heading", font=('Segoe UI', 10, 'bold'))
        style.configure("Treeview", font=('Segoe UI', 9))

        # Scrollbars with modern styling
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # Add row highlighting
        self.tree.tag_configure('evenrow', background='#f8f9fa')
        self.tree.tag_configure('oddrow', background='#ffffff')

    def on_search_change(self, event=None):
        """Handle search input changes"""
        if hasattr(self, 'search_var') and self.df is not None:
            # Debounce search to avoid too many updates
            if hasattr(self, 'search_timer'):
                self.root.after_cancel(self.search_timer)
            self.search_timer = self.root.after(300, self.perform_search)

    def perform_search(self):
        """Perform the actual search across all columns"""
        search_term = self.search_var.get().strip().lower()
        if not search_term or self.df is None:
            self.apply_filters()
            return

        try:
            # Search across all string columns
            mask = pd.Series([False] * len(self.df))
            for column in self.df.columns:
                if self.df[column].dtype == 'object':
                    mask |= self.df[column].astype(str).str.lower().str.contains(search_term, na=False)

            # Apply search filter along with other filters
            search_df = self.df[mask]

            # Apply other filters on top of search results
            if hasattr(self, 'filter_vars'):
                for column, filter_info in self.filter_vars.items():
                    if filter_info['type'] == 'text':
                        filter_value = filter_info['var'].get().strip()
                        if filter_value:
                            mask = search_df[column].astype(str).str.contains(
                                filter_value, case=False, na=False)
                            search_df = search_df[mask]
                    elif filter_info['type'] == 'numeric':
                        min_val = filter_info['min_var'].get().strip()
                        max_val = filter_info['max_var'].get().strip()

                        if min_val:
                            try:
                                min_num = float(min_val)
                                search_df = search_df[search_df[column] >= min_num]
                            except ValueError:
                                pass

                        if max_val:
                            try:
                                max_num = float(max_val)
                                search_df = search_df[search_df[column] <= max_num]
                            except ValueError:
                                pass

            self.filtered_df = search_df
            self.update_results_display()

        except Exception as e:
            print(f"Search error: {e}")

    def save_filter_preset(self):
        """Save current filter settings as a preset"""
        if not hasattr(self, 'filter_vars') or not self.filter_vars:
            messagebox.showwarning("No Filters", "No filters are currently set to save.")
            return

        preset_name = tk.simpledialog.askstring("Save Preset", "Enter a name for this filter preset:")
        if not preset_name:
            return

        # Collect current filter values
        preset_data = {}
        for column, filter_info in self.filter_vars.items():
            if filter_info['type'] == 'text':
                value = filter_info['var'].get().strip()
                if value:
                    preset_data[column] = {'type': 'text', 'value': value}
            elif filter_info['type'] == 'numeric':
                min_val = filter_info['min_var'].get().strip()
                max_val = filter_info['max_var'].get().strip()
                if min_val or max_val:
                    preset_data[column] = {
                        'type': 'numeric',
                        'min_val': min_val,
                        'max_val': max_val
                    }

        if not preset_data:
            messagebox.showwarning("No Filters", "No filters are currently set to save.")
            return

        self.filter_presets[preset_name] = preset_data
        self.save_filter_presets()
        messagebox.showinfo("Success", f"Filter preset '{preset_name}' saved successfully!")

    def load_filter_preset(self):
        """Load a saved filter preset"""
        if not self.filter_presets:
            messagebox.showinfo("No Presets", "No filter presets have been saved yet.")
            return

        # Create selection dialog
        preset_names = list(self.filter_presets.keys())
        selected = tk.simpledialog.askstring(
            "Load Preset",
            f"Available presets:\n" + "\n".join(f"• {name}" for name in preset_names) +
            "\n\nEnter preset name to load:"
        )

        if not selected or selected not in self.filter_presets:
            return

        # Clear current filters
        self.clear_filters()

        # Load preset values
        preset_data = self.filter_presets[selected]
        for column, filter_data in preset_data.items():
            if column in self.filter_vars:
                filter_info = self.filter_vars[column]
                if filter_data['type'] == 'text' and filter_info['type'] == 'text':
                    filter_info['var'].set(filter_data['value'])
                elif filter_data['type'] == 'numeric' and filter_info['type'] == 'numeric':
                    filter_info['min_var'].set(filter_data.get('min_val', ''))
                    filter_info['max_var'].set(filter_data.get('max_val', ''))

        # Apply the loaded filters
        self.apply_filters()
        messagebox.showinfo("Success", f"Filter preset '{selected}' loaded successfully!")

    def browse_file(self):
        """Open file dialog to select Excel file"""
        file_path = filedialog.askopenfilename(
            title="Select Excel File",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.load_excel_file(file_path)

    def load_excel_file(self, file_path: str):
        """Load Excel file and display basic information"""
        try:
            # Show loading status
            self.status_label.config(text="🔄 Loading...")
            self.root.update()

            # Validate file exists
            if not os.path.exists(file_path):
                messagebox.showerror("Error", "File does not exist")
                self.status_label.config(text="❌ Error")
                return

            # Validate file extension
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                messagebox.showerror("Error", "Please select a valid Excel file (.xlsx or .xls)")
                self.status_label.config(text="❌ Error")
                return

            # Load the Excel file
            self.df = pd.read_excel(file_path)

            # Validate DataFrame is not empty
            if self.df.empty:
                messagebox.showwarning("Warning", "The Excel file is empty")
                self.status_label.config(text="⚠️ Empty")
                return

            self.filtered_df = self.df.copy()
            self.file_path = file_path

            # Add to recent files
            self.add_recent_file(file_path)

            # Update file label with modern styling
            filename = os.path.basename(file_path)
            self.file_label.config(text=f"✅ {filename}")
            self.status_label.config(text="✅ Loaded")

            # Display file information
            self.display_file_info()

            # Create filter controls
            self.create_filter_controls()

            # Display initial data
            self.update_results_display()

            # Clear search
            if hasattr(self, 'search_var'):
                self.search_var.set('')

        except pd.errors.EmptyDataError:
            messagebox.showerror("Error", "The Excel file is empty or corrupted")
            self.status_label.config(text="❌ Error")
        except pd.errors.ParserError:
            messagebox.showerror("Error", "Unable to parse the Excel file. Please check the file format.")
            self.status_label.config(text="❌ Error")
        except PermissionError:
            messagebox.showerror("Error", "Permission denied. The file might be open in another application.")
            self.status_label.config(text="❌ Error")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load Excel file:\n{str(e)}")
            self.status_label.config(text="❌ Error")

    def display_file_info(self):
        """Display basic information about the loaded file"""
        if self.df is not None:
            # Calculate file size
            memory_usage = self.df.memory_usage(deep=True).sum()
            if memory_usage < 1024:
                size_str = f"{memory_usage} B"
            elif memory_usage < 1024**2:
                size_str = f"{memory_usage/1024:.1f} KB"
            elif memory_usage < 1024**3:
                size_str = f"{memory_usage/(1024**2):.1f} MB"
            else:
                size_str = f"{memory_usage/(1024**3):.1f} GB"

            # Update info labels
            self.rows_label.config(text=f"📊 Rows: {len(self.df):,}")
            self.cols_label.config(text=f"📋 Columns: {len(self.df.columns)}")
            self.size_label.config(text=f"💾 Size: {size_str}")

    def create_filter_controls(self):
        """Create dynamic filter controls based on DataFrame columns"""
        # Clear existing filter controls
        for widget in self.filter_controls_frame.winfo_children():
            widget.destroy()

        if self.df is None:
            return

        # Store filter variables
        self.filter_vars = {}

        # Create scrollable frame for filters
        canvas = tk.Canvas(self.filter_controls_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.filter_controls_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Column filters header
        header_frame = ttk.Frame(scrollable_frame)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        header_frame.columnconfigure(0, weight=1)

        ttk.Label(header_frame, text="📋 Column Filters:", style="Heading.TLabel").grid(row=0, column=0, sticky=tk.W)

        # Add filter controls for each column
        for i, column in enumerate(self.df.columns):
            self.create_column_filter(scrollable_frame, column, i + 1)

        # Action buttons with modern styling
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.grid(row=len(self.df.columns) + 2, column=0, sticky=(tk.W, tk.E), pady=(20, 0))
        button_frame.columnconfigure(0, weight=1)

        ttk.Button(button_frame, text="✅ Apply Filters", style="Primary.TButton",
                  command=self.apply_filters).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Button(button_frame, text="🗑️ Clear All", style="Secondary.TButton",
                  command=self.clear_filters).grid(row=1, column=0, sticky=(tk.W, tk.E))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)

    def create_column_filter(self, parent, column, row):
        """Create filter controls for a specific column"""
        # Create a frame for each filter
        filter_frame = ttk.Frame(parent, padding="5")
        filter_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        filter_frame.columnconfigure(1, weight=1)

        # Column label with type indicator
        col_data = self.df[column].dropna()
        if col_data.dtype in ['object', 'string']:
            type_icon = "📝"
        elif col_data.dtype in ['int64', 'float64']:
            type_icon = "🔢"
        else:
            type_icon = "📄"

        ttk.Label(filter_frame, text=f"{type_icon} {column}", style="Info.TLabel").grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10))

        # Determine column data type and create appropriate filter
        if col_data.dtype in ['object', 'string']:
            # Text filter for string columns
            self.create_text_filter(filter_frame, column, row)
        elif col_data.dtype in ['int64', 'float64']:
            # Numeric range filter
            self.create_numeric_filter(filter_frame, column, row)
        else:
            # Default text filter
            self.create_text_filter(filter_frame, column, row)

    def create_text_filter(self, parent, column, row):
        """Create text-based filter controls"""
        filter_var = tk.StringVar()
        self.filter_vars[column] = {'type': 'text', 'var': filter_var}

        # Create input frame
        input_frame = ttk.Frame(parent)
        input_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        input_frame.columnconfigure(0, weight=1)

        entry = ttk.Entry(input_frame, textvariable=filter_var, font=('Segoe UI', 9))
        entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # Add unique values dropdown for reasonable number of values
        unique_values = sorted(self.df[column].dropna().astype(str).unique())
        if len(unique_values) <= 50:
            combo = ttk.Combobox(input_frame, values=[''] + unique_values,
                               width=12, font=('Segoe UI', 8))
            combo.grid(row=0, column=1)
            combo.bind('<<ComboboxSelected>>',
                      lambda e: filter_var.set(combo.get()))

        # Add placeholder text
        entry.insert(0, "Type to filter...")
        entry.bind('<FocusIn>', lambda e: entry.delete(0, tk.END) if entry.get() == "Type to filter..." else None)
        entry.bind('<FocusOut>', lambda e: entry.insert(0, "Type to filter...") if not entry.get() else None)

    def create_numeric_filter(self, parent, column, row):
        """Create numeric range filter controls"""
        min_val = self.df[column].min()
        max_val = self.df[column].max()

        # Min and max variables
        min_var = tk.StringVar()
        max_var = tk.StringVar()

        self.filter_vars[column] = {
            'type': 'numeric',
            'min_var': min_var,
            'max_var': max_var,
            'min_val': min_val,
            'max_val': max_val
        }

        # Create range input frame
        range_frame = ttk.Frame(parent)
        range_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        range_frame.columnconfigure(1, weight=1)
        range_frame.columnconfigure(3, weight=1)

        ttk.Label(range_frame, text="Min:", style="Info.TLabel").grid(row=0, column=0, sticky=tk.W)
        min_entry = ttk.Entry(range_frame, textvariable=min_var, width=8, font=('Segoe UI', 9))
        min_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 10))

        ttk.Label(range_frame, text="Max:", style="Info.TLabel").grid(row=0, column=2, sticky=tk.W)
        max_entry = ttk.Entry(range_frame, textvariable=max_var, width=8, font=('Segoe UI', 9))
        max_entry.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(5, 0))

        # Range info
        range_info = ttk.Label(range_frame, text=f"Range: {min_val:.2f} - {max_val:.2f}",
                              style="Info.TLabel")
        range_info.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(5, 0))

    def apply_filters(self):
        """Apply all active filters to the DataFrame"""
        if self.df is None:
            return

        try:
            # Start with the original DataFrame
            filtered_df = self.df.copy()

            # Apply each filter
            for column, filter_info in self.filter_vars.items():
                if filter_info['type'] == 'text':
                    filter_value = filter_info['var'].get().strip()
                    if filter_value:
                        # Case-insensitive partial match
                        mask = filtered_df[column].astype(str).str.contains(
                            filter_value, case=False, na=False)
                        filtered_df = filtered_df[mask]

                elif filter_info['type'] == 'numeric':
                    min_val = filter_info['min_var'].get().strip()
                    max_val = filter_info['max_var'].get().strip()

                    if min_val:
                        try:
                            min_num = float(min_val)
                            filtered_df = filtered_df[filtered_df[column] >= min_num]
                        except ValueError:
                            pass

                    if max_val:
                        try:
                            max_num = float(max_val)
                            filtered_df = filtered_df[filtered_df[column] <= max_num]
                        except ValueError:
                            pass

            self.filtered_df = filtered_df
            self.update_results_display()

        except Exception as e:
            messagebox.showerror("Filter Error", f"Error applying filters:\n{str(e)}")

    def clear_filters(self):
        """Clear all filter inputs and reset to original data"""
        if self.df is None:
            return

        # Clear all filter variables
        for column, filter_info in self.filter_vars.items():
            if filter_info['type'] == 'text':
                filter_info['var'].set('')
            elif filter_info['type'] == 'numeric':
                filter_info['min_var'].set('')
                filter_info['max_var'].set('')

        # Reset to original data
        self.filtered_df = self.df.copy()
        self.update_results_display()

    def update_results_display(self):
        """Update the results table with filtered data"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)

        if self.filtered_df is None or self.filtered_df.empty:
            self.result_label.config(text="📭 No data to display")
            return

        # Configure columns
        columns = list(self.filtered_df.columns)
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'

        # Configure column headings and widths with better sizing
        for col in columns:
            self.tree.heading(col, text=col, anchor='w')
            # Auto-size columns based on content
            max_width = max(
                len(str(col)) * 8,  # Header width
                max([len(str(val)) for val in self.filtered_df[col].head(100)]) * 8 if not self.filtered_df[col].empty else 50
            )
            self.tree.column(col, width=min(max_width, 200), minwidth=80, anchor='w')

        # Insert data (limit to first 1000 rows for performance) with alternating colors
        display_df = self.filtered_df.head(1000)
        for index, (_, row) in enumerate(display_df.iterrows()):
            values = [str(val) if pd.notna(val) else '' for val in row]
            tag = 'evenrow' if index % 2 == 0 else 'oddrow'
            self.tree.insert('', 'end', values=values, tags=(tag,))

        # Update result label with better formatting
        total_rows = len(self.filtered_df)
        displayed_rows = len(display_df)

        if total_rows > 1000:
            self.result_label.config(text=f"📊 Showing {displayed_rows:,} of {total_rows:,} rows")
        else:
            self.result_label.config(text=f"📊 Showing {total_rows:,} rows")

        # Add filter status
        if hasattr(self, 'filter_vars'):
            active_filters = sum(1 for filter_info in self.filter_vars.values()
                               if (filter_info['type'] == 'text' and filter_info['var'].get().strip()) or
                                  (filter_info['type'] == 'numeric' and (filter_info['min_var'].get().strip() or filter_info['max_var'].get().strip())))
            if active_filters > 0:
                current_text = self.result_label.cget('text')
                self.result_label.config(text=f"{current_text} | 🔍 {active_filters} filter(s) active")

    def export_data(self):
        """Export filtered data to a new Excel file"""
        if self.filtered_df is None or self.filtered_df.empty:
            messagebox.showwarning("No Data", "No filtered data to export")
            return

        # Get save file path
        file_path = filedialog.asksaveasfilename(
            title="Save Filtered Data",
            defaultextension=".xlsx",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                self.filtered_df.to_excel(file_path, index=False)
                messagebox.showinfo("Success", f"Data exported successfully to:\n{file_path}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export data:\n{str(e)}")

    def show_about(self):
        """Show about dialog"""
        about_text = """Excel Filtering Tool v1.0

A desktop application for filtering Excel files with an intuitive GUI.

Features:
• Load Excel files (.xlsx, .xls)
• Apply text and numeric filters
• View filtered results in real-time
• Export filtered data to new Excel file

Keyboard Shortcuts:
• Ctrl+O: Open Excel file
• Ctrl+S: Export filtered data
• F5: Apply filters
• Esc: Clear all filters

Developed with Python, pandas, and tkinter."""

        messagebox.showinfo("About Excel Filtering Tool", about_text)


if __name__ == "__main__":
    root = TkinterDnD.Tk()
    app = ExcelFilterTool(root)
    root.mainloop()
