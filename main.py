import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
from typing import Optional, Dict, Any


class ExcelFilterTool:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel Filtering Tool")
        self.root.geometry("1200x800")

        # Data storage
        self.df: Optional[pd.DataFrame] = None
        self.filtered_df: Optional[pd.DataFrame] = None
        self.file_path: Optional[str] = None

        # Bind keyboard shortcuts
        self.root.bind('<Control-o>', lambda e: self.browse_file())
        self.root.bind('<Control-s>', lambda e: self.export_data())
        self.root.bind('<F5>', lambda e: self.apply_filters())
        self.root.bind('<Escape>', lambda e: self.clear_filters())

        # Create main interface
        self.create_menu()
        self.create_widgets()

    def create_menu(self):
        """Create menu bar with keyboard shortcuts"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Excel File...", command=self.browse_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Export Filtered Data...", command=self.export_data, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Filter menu
        filter_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Filter", menu=filter_menu)
        filter_menu.add_command(label="Apply Filters", command=self.apply_filters, accelerator="F5")
        filter_menu.add_command(label="Clear All Filters", command=self.clear_filters, accelerator="Esc")

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)

    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="Browse Excel File", 
                  command=self.browse_file).grid(row=0, column=0, padx=(0, 10))
        
        self.file_label = ttk.Label(file_frame, text="No file selected")
        self.file_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # File info section
        self.info_frame = ttk.LabelFrame(main_frame, text="File Information", padding="5")
        self.info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.info_text = tk.Text(self.info_frame, height=4, state='disabled')
        self.info_text.pack(fill='x')
        
        # Filter section
        self.filter_frame = ttk.LabelFrame(main_frame, text="Filters", padding="5")
        self.filter_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="5")
        results_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results table with scrollbars
        table_frame = ttk.Frame(results_frame)
        table_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        self.tree = ttk.Treeview(table_frame)
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbars for results table
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Export button
        export_frame = ttk.Frame(results_frame)
        export_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(export_frame, text="Export Filtered Data", 
                  command=self.export_data).pack(side=tk.RIGHT)
        
        self.result_label = ttk.Label(export_frame, text="")
        self.result_label.pack(side=tk.LEFT)
        
    def browse_file(self):
        """Open file dialog to select Excel file"""
        file_path = filedialog.askopenfilename(
            title="Select Excel File",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_excel_file(file_path)
    
    def load_excel_file(self, file_path: str):
        """Load Excel file and display basic information"""
        try:
            # Validate file exists
            if not os.path.exists(file_path):
                messagebox.showerror("Error", "File does not exist")
                return

            # Validate file extension
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                messagebox.showerror("Error", "Please select a valid Excel file (.xlsx or .xls)")
                return

            # Load the Excel file
            self.df = pd.read_excel(file_path)

            # Validate DataFrame is not empty
            if self.df.empty:
                messagebox.showwarning("Warning", "The Excel file is empty")
                return

            self.filtered_df = self.df.copy()
            self.file_path = file_path

            # Update file label
            filename = os.path.basename(file_path)
            self.file_label.config(text=f"Selected: {filename}")

            # Display file information
            self.display_file_info()

            # Create filter controls
            self.create_filter_controls()

            # Display initial data
            self.update_results_display()

        except pd.errors.EmptyDataError:
            messagebox.showerror("Error", "The Excel file is empty or corrupted")
        except pd.errors.ParserError:
            messagebox.showerror("Error", "Unable to parse the Excel file. Please check the file format.")
        except PermissionError:
            messagebox.showerror("Error", "Permission denied. The file might be open in another application.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load Excel file:\n{str(e)}")
    
    def display_file_info(self):
        """Display basic information about the loaded file"""
        if self.df is not None:
            info_text = f"Rows: {len(self.df)}\n"
            info_text += f"Columns: {len(self.df.columns)}\n"
            info_text += f"Column names: {', '.join(self.df.columns.tolist())}"

            self.info_text.config(state='normal')
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)
            self.info_text.config(state='disabled')

    def create_filter_controls(self):
        """Create dynamic filter controls based on DataFrame columns"""
        # Clear existing filter controls
        for widget in self.filter_frame.winfo_children():
            widget.destroy()

        if self.df is None:
            return

        # Store filter variables
        self.filter_vars = {}

        # Create scrollable frame for filters
        canvas = tk.Canvas(self.filter_frame)
        scrollbar = ttk.Scrollbar(self.filter_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Add filter controls for each column
        for i, column in enumerate(self.df.columns):
            self.create_column_filter(scrollable_frame, column, i)

        # Apply filters button
        ttk.Button(scrollable_frame, text="Apply Filters",
                  command=self.apply_filters).grid(row=len(self.df.columns), column=0,
                                                  columnspan=3, pady=10, sticky=(tk.W, tk.E))

        # Clear filters button
        ttk.Button(scrollable_frame, text="Clear All Filters",
                  command=self.clear_filters).grid(row=len(self.df.columns)+1, column=0,
                                                  columnspan=3, pady=5, sticky=(tk.W, tk.E))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_column_filter(self, parent, column, row):
        """Create filter controls for a specific column"""
        # Column label
        ttk.Label(parent, text=column).grid(row=row, column=0, sticky=tk.W, padx=(0, 10))

        # Determine column data type
        col_data = self.df[column].dropna()

        if col_data.dtype in ['object', 'string']:
            # Text filter for string columns
            self.create_text_filter(parent, column, row)
        elif col_data.dtype in ['int64', 'float64']:
            # Numeric range filter
            self.create_numeric_filter(parent, column, row)
        else:
            # Default text filter
            self.create_text_filter(parent, column, row)

    def create_text_filter(self, parent, column, row):
        """Create text-based filter controls"""
        filter_var = tk.StringVar()
        self.filter_vars[column] = {'type': 'text', 'var': filter_var}

        entry = ttk.Entry(parent, textvariable=filter_var, width=20)
        entry.grid(row=row, column=1, padx=5, sticky=(tk.W, tk.E))

        # Add unique values dropdown
        unique_values = sorted(self.df[column].dropna().astype(str).unique())
        if len(unique_values) <= 50:  # Only show dropdown for reasonable number of values
            combo = ttk.Combobox(parent, values=[''] + unique_values, width=15)
            combo.grid(row=row, column=2, padx=5)
            combo.bind('<<ComboboxSelected>>',
                      lambda e: filter_var.set(combo.get()))

    def create_numeric_filter(self, parent, column, row):
        """Create numeric range filter controls"""
        min_val = self.df[column].min()
        max_val = self.df[column].max()

        # Min value
        min_var = tk.StringVar()
        max_var = tk.StringVar()

        self.filter_vars[column] = {
            'type': 'numeric',
            'min_var': min_var,
            'max_var': max_var,
            'min_val': min_val,
            'max_val': max_val
        }

        frame = ttk.Frame(parent)
        frame.grid(row=row, column=1, columnspan=2, sticky=(tk.W, tk.E))

        ttk.Label(frame, text="Min:").pack(side=tk.LEFT)
        ttk.Entry(frame, textvariable=min_var, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Label(frame, text="Max:").pack(side=tk.LEFT)
        ttk.Entry(frame, textvariable=max_var, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Label(frame, text=f"({min_val:.2f} - {max_val:.2f})").pack(side=tk.LEFT, padx=5)

    def apply_filters(self):
        """Apply all active filters to the DataFrame"""
        if self.df is None:
            return

        try:
            # Start with the original DataFrame
            filtered_df = self.df.copy()

            # Apply each filter
            for column, filter_info in self.filter_vars.items():
                if filter_info['type'] == 'text':
                    filter_value = filter_info['var'].get().strip()
                    if filter_value:
                        # Case-insensitive partial match
                        mask = filtered_df[column].astype(str).str.contains(
                            filter_value, case=False, na=False)
                        filtered_df = filtered_df[mask]

                elif filter_info['type'] == 'numeric':
                    min_val = filter_info['min_var'].get().strip()
                    max_val = filter_info['max_var'].get().strip()

                    if min_val:
                        try:
                            min_num = float(min_val)
                            filtered_df = filtered_df[filtered_df[column] >= min_num]
                        except ValueError:
                            pass

                    if max_val:
                        try:
                            max_num = float(max_val)
                            filtered_df = filtered_df[filtered_df[column] <= max_num]
                        except ValueError:
                            pass

            self.filtered_df = filtered_df
            self.update_results_display()

        except Exception as e:
            messagebox.showerror("Filter Error", f"Error applying filters:\n{str(e)}")

    def clear_filters(self):
        """Clear all filter inputs and reset to original data"""
        if self.df is None:
            return

        # Clear all filter variables
        for column, filter_info in self.filter_vars.items():
            if filter_info['type'] == 'text':
                filter_info['var'].set('')
            elif filter_info['type'] == 'numeric':
                filter_info['min_var'].set('')
                filter_info['max_var'].set('')

        # Reset to original data
        self.filtered_df = self.df.copy()
        self.update_results_display()

    def update_results_display(self):
        """Update the results table with filtered data"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)

        if self.filtered_df is None or self.filtered_df.empty:
            self.result_label.config(text="No data to display")
            return

        # Configure columns
        columns = list(self.filtered_df.columns)
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'

        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, minwidth=50)

        # Insert data (limit to first 1000 rows for performance)
        display_df = self.filtered_df.head(1000)
        for index, row in display_df.iterrows():
            values = [str(val) if pd.notna(val) else '' for val in row]
            self.tree.insert('', 'end', values=values)

        # Update result label
        total_rows = len(self.filtered_df)
        displayed_rows = len(display_df)
        if total_rows > 1000:
            self.result_label.config(text=f"Showing {displayed_rows} of {total_rows} rows")
        else:
            self.result_label.config(text=f"Showing {total_rows} rows")

    def export_data(self):
        """Export filtered data to a new Excel file"""
        if self.filtered_df is None or self.filtered_df.empty:
            messagebox.showwarning("No Data", "No filtered data to export")
            return

        # Get save file path
        file_path = filedialog.asksaveasfilename(
            title="Save Filtered Data",
            defaultextension=".xlsx",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                self.filtered_df.to_excel(file_path, index=False)
                messagebox.showinfo("Success", f"Data exported successfully to:\n{file_path}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export data:\n{str(e)}")

    def show_about(self):
        """Show about dialog"""
        about_text = """Excel Filtering Tool v1.0

A desktop application for filtering Excel files with an intuitive GUI.

Features:
• Load Excel files (.xlsx, .xls)
• Apply text and numeric filters
• View filtered results in real-time
• Export filtered data to new Excel file

Keyboard Shortcuts:
• Ctrl+O: Open Excel file
• Ctrl+S: Export filtered data
• F5: Apply filters
• Esc: Clear all filters

Developed with Python, pandas, and tkinter."""

        messagebox.showinfo("About Excel Filtering Tool", about_text)


if __name__ == "__main__":
    root = tk.Tk()
    app = ExcelFilterTool(root)
    root.mainloop()
